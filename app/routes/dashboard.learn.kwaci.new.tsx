import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData, useActionData, useNavigation, Form, Link } from "react-router";
import { useState, useEffect } from "react";
import { z } from "zod";
import { requireAuthSession } from "~/lib/session.server";
import { ServerApiClient } from "~/lib/data-fetching";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { KWACIPrimerForm } from "~/components/learn";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { ArrowLeft, CheckCircle, AlertCircle, Clock, Sparkles } from "lucide-react";

export const meta: MetaFunction = () => {
  return [
    { title: "Create KWACI Primer - Kwaci Learning" },
    { name: "description", content: "Generate a comprehensive KWACI primer with structured learning content" },
  ];
};

// KWACI form validation schema
const kwaciFormSchema = z.object({
  topic: z.string().min(3, "Topic must be at least 3 characters"),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]),
  focusAreas: z.string().optional(),
});

type KWACIFormData = z.infer<typeof kwaciFormSchema>;

export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  // Extract URL parameters for pre-filling form
  const initialFormValues = {
    topic: url.searchParams.get("topic") || "",
    learningLevel: url.searchParams.get("learningLevel") as "beginner" | "intermediate" | "advanced" || "beginner",
    focusAreas: url.searchParams.get("focusAreas") || "",
  };

  return {
    user: authSession.user,
    initialFormValues,
  };
}

export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return { success: false, error: "Method not allowed" };
  }

  try {
    const formData = await request.formData();
    const intent = formData.get("intent");

    if (intent === "generate") {
      // Parse form data
      const data = {
        topic: formData.get("topic") as string,
        learningLevel: formData.get("learningLevel") as string,
        focusAreas: formData.get("focusAreas") as string,
      };

      // Validate input
      const validatedData = kwaciFormSchema.parse(data);

      // Call content generation API with KWACI-specific parameters
      const generateResult = await ServerApiClient.generateContent(request, {
        topic: validatedData.topic,
        learningLevel: validatedData.learningLevel,
        preferredContentTypes: ["paragraph", "infoBox", "bulletList", "numberedList"], // KWACI uses all types
        additionalContext: `Generate a comprehensive KWACI primer with 9 structured sections: ${validatedData.focusAreas || ""}`,
        contentType: "kwaci-primer",
      });

      if (!generateResult) {
        return {
          success: false,
          error: "Failed to generate KWACI primer",
        };
      }

      // Save the generated KWACI primer
      const saveResult = await ServerApiClient.saveLearningContent(request, {
        title: `KWACI Primer: ${validatedData.topic}`,
        description: `Comprehensive KWACI primer covering ${validatedData.topic} with structured 9-section approach`,
        content: generateResult,
        contentType: "kwaci-primer",
        learningLevel: validatedData.learningLevel,
        isPublic: true,
        aiMetadata: {
          topic: validatedData.topic,
          contentType: "kwaci-primer",
          focusAreas: validatedData.focusAreas,
          sections: 9,
        },
      });

      if (!saveResult) {
        return {
          success: false,
          error: "KWACI primer generated but failed to save",
        };
      }

      return {
        success: true,
        contentId: saveResult.id,
        message: "KWACI primer generated successfully!",
      };
    }

    return { success: false, error: "Invalid intent" };
  } catch (error) {
    console.error("Error in KWACI action:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid form data",
        details: error.errors,
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}

export default function KWACINewPage() {
  const { user, initialFormValues } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const [generationStage, setGenerationStage] = useState<"idle" | "generating" | "success" | "error">("idle");
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState("");

  const isGenerating = navigation.state === "submitting" || generationStage === "generating";

  // Handle action results
  useEffect(() => {
    if (actionData) {
      if (actionData.success) {
        setGenerationStage("success");
        setGenerationProgress(100);
        setGenerationStatus("KWACI primer generated successfully!");
        
        // Redirect to content view after delay
        setTimeout(() => {
          window.location.href = `/dashboard/learn/${actionData.contentId}`;
        }, 2000);
      } else {
        setGenerationStage("error");
        setGenerationProgress(0);
      }
    }
  }, [actionData]);

  // Simulate progress during generation
  useEffect(() => {
    if (isGenerating && generationStage !== "generating") {
      setGenerationStage("generating");
      setGenerationProgress(0);
      
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) return 90;
          return prev + Math.random() * 10;
        });
      }, 800);

      const statusMessages = [
        "Analyzing your topic for KWACI structure...",
        "Creating knowledge framework...",
        "Developing analogies and examples...",
        "Breaking down core components...",
        "Generating implementation details...",
        "Structuring 9-section primer...",
        "Finalizing comprehensive content...",
      ];

      let statusIndex = 0;
      const statusInterval = setInterval(() => {
        if (statusIndex < statusMessages.length) {
          setGenerationStatus(statusMessages[statusIndex]);
          statusIndex++;
        }
      }, 1200);

      return () => {
        clearInterval(progressInterval);
        clearInterval(statusInterval);
      };
    }
  }, [isGenerating, generationStage]);

  const handleFormSubmit = (data: KWACIFormData) => {
    console.log("KWACI form data:", data);
  };

  // Show loading state during generation
  if (generationStage === "generating") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Link to="/dashboard/learn">
              <Button variant="outline" disabled>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Learn
              </Button>
            </Link>
          </div>

          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <div className="mb-6">
                  <Sparkles className="h-16 w-16 text-purple-500 mx-auto animate-pulse" />
                </div>
                <h2 className="text-2xl font-bold mb-4">
                  Generating Your KWACI Primer
                </h2>
                <p className="text-muted-foreground mb-6">
                  Creating a comprehensive 9-section primer with knowledge, analogies, components, and implementation details.
                </p>

                <Progress value={generationProgress} className="mb-4" />

                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 animate-spin" />
                  <span>{generationStatus || "Preparing KWACI structure..."}</span>
                </div>

                <p className="text-xs text-muted-foreground mt-2">
                  {Math.round(generationProgress)}% complete
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show success state
  if (generationStage === "success") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Link to="/dashboard/learn">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Learn
              </Button>
            </Link>
          </div>

          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">
                  KWACI Primer Generated Successfully!
                </h2>
                <p className="text-muted-foreground mb-6">
                  Your comprehensive KWACI primer is ready with all 9 structured sections.
                </p>

                {actionData?.contentId && (
                  <Link to={`/dashboard/learn/${actionData.contentId}`}>
                    <Button className="bg-purple-600 hover:bg-purple-700">
                      <Sparkles className="h-4 w-4 mr-2" />
                      View Your KWACI Primer
                    </Button>
                  </Link>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (generationStage === "error") {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Link to="/dashboard/learn">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Learn
              </Button>
            </Link>
          </div>

          <Card>
            <CardContent className="p-8">
              <div className="text-center">
                <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">
                  Generation Failed
                </h2>
                <p className="text-muted-foreground mb-6">
                  {actionData?.error || "An error occurred while generating your KWACI primer."}
                </p>

                <div className="space-x-4">
                  <Button
                    onClick={() => {
                      setGenerationStage("idle");
                      setGenerationProgress(0);
                    }}
                  >
                    Try Again
                  </Button>
                  <Link to="/dashboard/learn">
                    <Button variant="outline">
                      Back to Learn
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Default form state
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Link to="/dashboard/learn">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Learn
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center mb-2">
              <Sparkles className="h-6 w-6 text-purple-600 mr-2" />
              <CardTitle className="text-3xl">Create KWACI Primer</CardTitle>
            </div>
            <CardDescription>
              Generate a comprehensive 9-section primer with Knowledge, Analogies, Components, and Implementation details
            </CardDescription>
          </CardHeader>
        </Card>

        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="p-6">
            <h3 className="font-medium mb-2">What is a KWACI Primer?</h3>
            <p className="text-sm text-muted-foreground mb-4">
              KWACI (Knowledge With Analogy, Components, and Implementation) is a structured learning approach that breaks down complex topics into 9 comprehensive sections:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <strong>Knowledge Foundation:</strong>
                <ul className="text-muted-foreground mt-1">
                  <li>• Overview & Context</li>
                  <li>• Core Concepts</li>
                  <li>• Key Principles</li>
                </ul>
              </div>
              <div>
                <strong>Understanding:</strong>
                <ul className="text-muted-foreground mt-1">
                  <li>• Analogies & Examples</li>
                  <li>• Component Breakdown</li>
                  <li>• Relationships</li>
                </ul>
              </div>
              <div>
                <strong>Application:</strong>
                <ul className="text-muted-foreground mt-1">
                  <li>• Implementation Steps</li>
                  <li>• Best Practices</li>
                  <li>• Common Pitfalls</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <Form method="post">
          <input type="hidden" name="intent" value="generate" />
          <KWACIPrimerForm
            onSubmit={handleFormSubmit}
            isLoading={isGenerating}
            initialValues={initialFormValues}
          />
        </Form>
      </div>
    </DashboardLayout>
  );
}
