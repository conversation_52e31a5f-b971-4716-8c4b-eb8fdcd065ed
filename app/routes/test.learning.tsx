import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData } from "react-router";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { EnhancedLearningContentDisplay } from "~/components/learn/EnhancedLearningContentDisplay";

export const meta: MetaFunction = () => {
  return [
    { title: "Test Learning Content - Kwaci Learning" },
    { name: "description", content: "Test the enhanced learning content display" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Mock learning content for testing the enhanced UI
  const content = {
    id: 'test-content-id',
    title: 'Introduction to React Hooks',
    description: 'This learning module will deepen your understanding of React Hooks, covering their purpose, usage, and practical applications in React development.',
    steps: [
      {
        id: '1',
        title: 'Understanding React Hooks',
        icon: '📚',
        blocks: [
          {
            id: 'block-1',
            type: 'paragraph',
            data: 'React Hooks are functions that let you use state and other React features without writing a class. They were introduced in React 16.8 to simplify component logic and improve code readability.'
          }
        ]
      },
      {
        id: '2',
        title: 'useState Hook',
        icon: '🔄',
        blocks: [
          {
            id: 'block-2',
            type: 'paragraph',
            data: 'The useState Hook allows you to add state to functional components. It returns an array with the current state value and a function to update it.'
          }
        ]
      },
      {
        id: '3',
        title: 'useEffect Hook',
        icon: '⚡',
        blocks: [
          {
            id: 'block-3',
            type: 'paragraph',
            data: 'The useEffect Hook lets you perform side effects in functional components. It serves the same purpose as componentDidMount, componentDidUpdate, and componentWillUnmount combined.'
          }
        ]
      },
      {
        id: '4',
        title: 'Custom Hooks',
        icon: '🛠️',
        blocks: [
          {
            id: 'block-4',
            type: 'paragraph',
            data: 'Custom Hooks are JavaScript functions whose names start with "use" and that may call other Hooks. They let you extract component logic into reusable functions.'
          }
        ]
      },
      {
        id: '5',
        title: 'Hook Rules',
        icon: '📋',
        blocks: [
          {
            id: 'block-5',
            type: 'paragraph',
            data: 'There are two main rules for using Hooks: only call Hooks at the top level of your React function, and only call Hooks from React functions.'
          }
        ]
      },
      {
        id: '6',
        title: 'Common Patterns',
        icon: '🎯',
        blocks: [
          {
            id: 'block-6',
            type: 'paragraph',
            data: 'Learn common patterns and best practices when using React Hooks, including how to optimize performance and avoid common pitfalls.'
          }
        ]
      },
      {
        id: '7',
        title: 'Advanced Hooks',
        icon: '🚀',
        blocks: [
          {
            id: 'block-7',
            type: 'paragraph',
            data: 'Explore advanced Hooks like useReducer, useContext, useMemo, and useCallback for more complex state management and performance optimization.'
          }
        ]
      }
    ],
    learningLevel: 'intermediate' as const,
    estimatedReadingTime: 8,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Transform database steps to component-expected format
  const transformedSteps = content.steps.map(step => ({
    title: step.title,
    icon: step.icon || '📚',
    type: 'paragraph' as const,
    data: step.blocks.map(block => block.data).join('\n')
  }));

  return {
    content: {
      ...content,
      steps: transformedSteps
    }
  };
}

export default function TestLearningPage() {
  const { content } = useLoaderData<typeof loader>();

  const handleStepChange = (step: number) => {
    console.log('Step changed to:', step);
  };

  const handleProgressUpdate = (progress: number) => {
    console.log('Progress updated to:', progress);
  };

  return (
    <DashboardLayout>
      <EnhancedLearningContentDisplay
        contentId={content.id}
        title={content.title}
        description={content.description}
        steps={content.steps}
        learningLevel={content.learningLevel}
        estimatedReadingTime={content.estimatedReadingTime}
        isPublic={content.isPublic}
        initialStep={0}
        completedSteps={[]}
        progress={29} // 29% as shown in screenshot
        onStepChange={handleStepChange}
        onProgressUpdate={handleProgressUpdate}
      />
    </DashboardLayout>
  );
}
