import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useLoaderData, Link } from "react-router";
import { requireAuthSession } from "~/lib/session.server";
import { ServerApiClient } from "~/lib/data-fetching";
import type { LearningContent } from "~/db/schema/learning-content";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { EnhancedLearningContentDisplay } from "~/components/learn/EnhancedLearningContentDisplay";
import { Button } from "~/components/ui/button";
import { ArrowLeft } from "lucide-react";

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `${data?.content?.title || "Learning Content"} - Kwaci Learning` },
    { name: "description", content: data?.content?.description || "View your learning content" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const { id } = params;

  if (!id) {
    throw new Response("Content ID is required", { status: 400 });
  }

  try {
    // ✅ Use typed API client for type-safe data fetching
    const content: LearningContent = await ServerApiClient.getLearningContent(request, id);

    return {
      user: authSession.user,
      content,
    };
  } catch (error) {
    console.error("Error loading learning content:", error);
    throw error;
  }
}

export default function LearnContentPage() {
  const { user, content } = useLoaderData<typeof loader>();

  // Transform database steps to component-expected format
  const transformedSteps = content.steps.map(step => ({
    title: step.title,
    icon: step.icon || '📚',
    type: 'paragraph' as const, // Default type, could be enhanced based on block types
    data: step.blocks.map(block => block.data).join('\n') // For paragraph type, data should be string
  }));

  const handleStepChange = (step: number) => {
    // TODO: Implement progress tracking
    console.log('Step changed to:', step);
  };

  const handleProgressUpdate = (progress: number) => {
    // TODO: Implement progress update
    console.log('Progress updated to:', progress);
  };

  return (
    <DashboardLayout>
      {/* Back Navigation */}
      <div className="flex items-center space-x-4 mb-6">
        <Link to="/dashboard/learn">
          <Button variant="ghost" size="sm" className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Learning</span>
          </Button>
        </Link>
      </div>

      {/* Enhanced Learning Content Display */}
      <EnhancedLearningContentDisplay
        contentId={content.id}
        title={content.title}
        description={content.description}
        steps={transformedSteps}
        learningLevel={content.learningLevel}
        estimatedReadingTime={content.estimatedReadingTime}
        isPublic={content.isPublic}
        initialStep={0}
        completedSteps={[]}
        progress={0}
        onStepChange={handleStepChange}
        onProgressUpdate={handleProgressUpdate}
      />
    </DashboardLayout>
  );
}
