import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, createQueryKey } from '../data-fetching';
import type {
  LearningContent,
  NewLearningContent,
  LearningContentListResponse
} from '~/db/schema/learning-content';

export interface GetLearningContentParams {
  query?: string;
  contentType?: 'standard' | 'kwaci-primer';
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  limit?: number;
  offset?: number;
}

export interface CreateLearningContentParams {
  title: string;
  description?: string;
  content: {
    steps: Array<{
      id: string;
      title: string;
      icon?: string;
      blocks: Array<{
        id: string;
        type: string;
        data: any;
        isEditing?: boolean;
      }>;
    }>;
    estimatedReadingTime?: number;
    metadata?: any;
  };
  contentType?: 'standard' | 'kwaci-primer';
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  aiMetadata?: {
    aiModel: string;
    generatedAt: string;
    contentTypes: string[];
    learningLevel: string;
    topic: string;
    preferredContentTypes?: string[];
  };
}

// Query hooks
export function useGetAllLearningContent(params: GetLearningContentParams = {}) {
  const {
    query,
    contentType,
    learningLevel,
    isPublic,
    limit = 100,
    offset = 0,
  } = params;

  return useQuery<LearningContent[], Error>({
    queryKey: createQueryKey('learningContent.getAll', {
      query,
      contentType,
      learningLevel,
      isPublic,
      limit,
      offset,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (query) searchParams.set('query', query);
      if (contentType) searchParams.set('contentType', contentType);
      if (learningLevel) searchParams.set('learningLevel', learningLevel);
      if (isPublic !== undefined) searchParams.set('isPublic', isPublic.toString());

      return apiClient.get<LearningContent[]>(`/learning?${searchParams}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetLearningContent(id: string) {
  return useQuery<LearningContent, Error>({
    queryKey: createQueryKey('learningContent.get', { id }),
    queryFn: async () => {
      return apiClient.get<LearningContent>(`/learning/${id}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutation hooks
export function useCreateLearningContent() {
  const queryClient = useQueryClient();

  return useMutation<LearningContent, Error, CreateLearningContentParams>({
    mutationFn: async (params) => {
      return apiClient.post('/learning', params);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

export function useUpdateLearningContent(id: string) {
  const queryClient = useQueryClient();

  return useMutation<LearningContent, Error, Partial<CreateLearningContentParams>>({
    mutationFn: async (params) => {
      return apiClient.put(`/learning/${id}`, params);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

export function useDeleteLearningContent() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      return apiClient.delete(`/learning/${id}`);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useLearningContentUtils() {
  const queryClient = useQueryClient();

  return {
    learningContent: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningContent.getAll'],
          });
        },
        refetch: (params?: GetLearningContentParams) => {
          const queryKey = createQueryKey('learningContent.getAll', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
      get: {
        invalidate: (id?: string) => {
          if (id) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('learningContent.get', { id }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['learningContent.get'],
            });
          }
        },
      },
    },
  };
}
