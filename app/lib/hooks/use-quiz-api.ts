import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, createQuery<PERSON><PERSON> } from '../data-fetching';

// Types for quiz API
export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: any[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  isPublic: boolean;
  authorId: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface GetQuizzesParams {
  limit?: number;
  offset?: number;
  includePublic?: boolean;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
}

export interface GetQuizHistoryParams {
  learningContentId?: string;
  limit?: number;
  offset?: number;
}

export interface CreateQuizParams {
  title: string;
  description?: string;
  questions: any[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  metadata?: any;
}

// Query hooks
export function useGetAllQuizzes(params: GetQuizzesParams = {}) {
  const {
    limit = 50,
    offset = 0,
    includePublic = true,
    difficulty,
  } = params;

  return useQuery<Quiz[], Error>({
    queryKey: createQueryKey('quiz.getAll', {
      limit,
      offset,
      includePublic,
      difficulty,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        includePublic: includePublic.toString(),
      });

      if (difficulty) searchParams.set('difficulty', difficulty);

      return apiClient.get<Quiz[]>(`/quiz?${searchParams}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetQuiz(id: string) {
  return useQuery<Quiz, Error>({
    queryKey: createQueryKey('quiz.get', { id }),
    queryFn: async () => {
      return apiClient.get<Quiz>(`/quiz/${id}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetQuizHistoryByLearningContent(params: GetQuizHistoryParams) {
  const { learningContentId, limit = 10, offset = 0 } = params;

  return useQuery<any[], Error>({
    queryKey: createQueryKey('quiz.getHistoryByLearningContent', {
      learningContentId,
      limit,
      offset,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (learningContentId) {
        searchParams.set('learningContentId', learningContentId);
      }

      return apiClient.get<any[]>(`/quiz/history?${searchParams}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!learningContentId,
  });
}

// Mutation hooks
export function useCreateQuiz() {
  const queryClient = useQueryClient();

  return useMutation<Quiz, Error, CreateQuizParams>({
    mutationFn: async (params) => {
      return apiClient.post('/quiz', params);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

export function useUpdateQuiz(id: string) {
  const queryClient = useQueryClient();

  return useMutation<Quiz, Error, Partial<CreateQuizParams>>({
    mutationFn: async (params) => {
      return apiClient.put(`/quiz/${id}`, params);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

export function useDeleteQuiz() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      return apiClient.delete(`/quiz/${id}`);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useQuizUtils() {
  const queryClient = useQueryClient();

  return {
    quiz: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quiz.getAll'],
          });
        },
        refetch: (params?: GetQuizzesParams) => {
          const queryKey = createQueryKey('quiz.getAll', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
      get: {
        invalidate: (id?: string) => {
          if (id) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('quiz.get', { id }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['quiz.get'],
            });
          }
        },
      },
    },
  };
}
