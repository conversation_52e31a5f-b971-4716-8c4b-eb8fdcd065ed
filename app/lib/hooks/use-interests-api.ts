import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, createQuery<PERSON>ey } from '../data-fetching';

// Types for interests API
export interface RecommendationItem {
  recommendation: {
    id: string;
    title: string;
    description: string;
    content: {
      learningLevel?: 'beginner' | 'intermediate' | 'advanced';
      preferredContentTypes?: string[];
      focusAreas?: string;
      estimatedTime?: number;
      difficulty?: string;
    };
    isViewed: boolean;
    isBookmarked: boolean;
  };
  topic: {
    id: string;
    name: string;
  };
}

export interface RecommendationsResponse {
  recommendations: RecommendationItem[];
  total: number;
  hasMore: boolean;
}

export interface GetRecommendationsParams {
  limit?: number;
  offset?: number;
  recommendationType?: 'content' | 'quiz' | 'topic';
  includeViewed?: boolean;
  includeCompleted?: boolean;
}

export interface MarkRecommendationViewedParams {
  recommendationId: string;
}

// Query hooks
export function useGetRecommendations(params: GetRecommendationsParams = {}) {
  const {
    limit = 4,
    offset = 0,
    recommendationType = 'content',
    includeViewed = false,
    includeCompleted = false,
  } = params;

  return useQuery<RecommendationsResponse, Error>({
    queryKey: createQueryKey('interests.recommendations', {
      limit,
      offset,
      recommendationType,
      includeViewed,
      includeCompleted,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        recommendationType,
        includeViewed: includeViewed.toString(),
        includeCompleted: includeCompleted.toString(),
      });

      return apiClient.get<RecommendationsResponse>(`/interests?${searchParams}`);
    },
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('UNAUTHORIZED') || error?.message?.includes('401')) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutation hooks
export function useMarkRecommendationViewed() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, MarkRecommendationViewedParams>({
    mutationFn: async (params) => {
      return apiClient.post('/interests', {
        action: 'mark_recommendation_viewed',
        recommendationId: params.recommendationId,
      });
    },
    onSuccess: () => {
      // Invalidate AI recommendations cache to refresh the recommendations list
      // This ensures the purple dot disappears and viewed recommendations are updated
      queryClient.invalidateQueries({
        queryKey: ['interests.recommendations'],
      });
    },
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useInterestsUtils() {
  const queryClient = useQueryClient();

  return {
    interests: {
      getRecommendations: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['interests.recommendations'],
          });
        },
        refetch: (params?: GetRecommendationsParams) => {
          const queryKey = createQueryKey('interests.recommendations', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
    },
  };
}
