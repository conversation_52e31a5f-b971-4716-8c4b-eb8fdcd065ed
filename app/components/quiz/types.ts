// Quiz-related type definitions

export interface QuizHistoryItem {
  id: string;
  quizId: string;
  userId: string;
  learningContentId?: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  completed: boolean;
  startedAt: string;
  completedAt?: string;
  answers: any[];
  metadata?: any;
}

export interface QuizStats {
  totalAttempts: number;
  completedAttempts: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
}

export interface QuizHistoryResponse {
  attempts: QuizHistoryItem[];
  stats: QuizStats;
  hasMore: boolean;
  total: number;
}

export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  isPublic: boolean;
  authorId: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'true_false' | 'short_answer';
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  points: number;
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  answers: QuizAnswer[];
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  completed: boolean;
  startedAt: string;
  completedAt?: string;
}

export interface QuizAnswer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  timeSpent: number;
}
