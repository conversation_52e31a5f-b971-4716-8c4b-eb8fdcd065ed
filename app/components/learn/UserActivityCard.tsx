'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Play } from 'lucide-react';
import { Link } from 'react-router';
// Note: Using placeholder data - can be connected to progress API later
import { cn } from '~/lib/utils';

interface UserActivityCardProps {
  className?: string;
}

export function UserActivityCard({ className = '' }: UserActivityCardProps) {
  // Placeholder data - replace with actual API calls when needed
  const quizData: { attempt?: { id: string; quizTitle: string; startedAt: string } } | null = null;
  const quizLoading = false;
  const quizError: Error | null = null;

  const learningData: { activity?: { id: string; content: { title: string }; updatedAt: string } } | null = null;
  const learningLoading = false;
  const learningError: Error | null = null;

  const isLoading = quizLoading || learningLoading;
  const hasError = Boolean(quizError || learningError);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
        <div className="animate-pulse">
          <div className="flex items-center mb-4">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded mr-4"></div>
            <div>
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-32 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-48"></div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="h-20 bg-gray-200 dark:bg-gray-600 rounded"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-600 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
        <div className="flex items-center mb-4">
          <Brain className="h-8 w-8 text-red-600" />
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h3>
            <p className="text-sm text-red-500">Unable to load activity data</p>
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {(quizError as unknown as Error)?.message || (learningError as unknown as Error)?.message || 'Something went wrong'}
          </p>
        </div>
      </div>
    );
  }

  // Extract in-progress activities from the new API responses
  const inProgressActivities: Array<{
    type: 'quiz' | 'learning';
    id: string;
    title: string;
    date: Date;
    data: any;
  }> = [];

  // Add in-progress quiz if exists
  if ((quizData as any)?.attempt) {
    const attempt = (quizData as any).attempt;
    inProgressActivities.push({
      type: 'quiz',
      id: attempt.id,
      title: attempt.quizTitle,
      date: new Date(attempt.startedAt),
      data: attempt
    });
  }

  // Add in-progress learning if exists
  if ((learningData as any)?.activity) {
    const activity = (learningData as any).activity;
    inProgressActivities.push({
      type: 'learning',
      id: activity.id,
      title: activity.content.title,
      date: new Date(activity.updatedAt),
      data: activity
    });
  }

  // Sort by date (most recent first) - maximum 2 items
  const sortedActivities = inProgressActivities
    .sort((a, b) => b.date.getTime() - a.date.getTime())
    .slice(0, 2);

  // Helper function to format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString();
  };



  return (
    <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
      {/* Header */}
      <div className="flex items-center mb-6">
        <div className="flex-shrink-0">
          <Brain className="h-8 w-8 text-blue-600" />
        </div>
        <div className="ml-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Continue Learning</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">Resume your in-progress activities</p>
        </div>
      </div>

      <div className="space-y-3">
        {/* Recent Activities List */}
        {sortedActivities.length > 0 ? (
          sortedActivities.map((activity) => (
            <div key={`${activity.type}-${activity.id}`} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    {activity.type === 'quiz' ? (
                      <Trophy className="h-4 w-4 text-indigo-600 mr-2" />
                    ) : (
                      <BookOpen className="h-4 w-4 text-purple-600 mr-2" />
                    )}
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {activity.type === 'quiz' ? 'Quiz' : 'Learning'}
                    </h4>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">{activity.title}</p>
                  <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                    {activity.type === 'quiz' ? (
                      <>
                        <span className="px-2 py-1 rounded-full font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                          In Progress
                        </span>
                        <span>{formatTimeAgo(activity.data.startedAt)}</span>
                      </>
                    ) : (
                      <>
                        <span className="px-2 py-1 rounded-full font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                          In Progress
                        </span>
                        {activity.data.completionPercentage && (
                          <span>{activity.data.completionPercentage}% complete</span>
                        )}
                        <span>{formatTimeAgo(activity.data.updatedAt)}</span>
                      </>
                    )}
                  </div>
                </div>
                {activity.type === 'quiz' ? (
                  <Link to={`/dashboard/quiz/${activity.data.quizId}?attemptId=${activity.data.id}`}>
                    <button className="ml-4 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                      <Play className="h-3 w-3 mr-1" />
                      Resume Quiz
                    </button>
                  </Link>
                ) : (
                  <Link to={`/dashboard/learn/${activity.data.content.id}`}>
                    <button className="ml-4 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-purple-600 bg-purple-100 hover:bg-purple-200">
                      <Play className="h-3 w-3 mr-1" />
                      Continue Learning
                    </button>
                  </Link>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
            <Brain className="h-8 w-8 text-gray-300 dark:text-gray-600 mx-auto mb-2" />
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">No in-progress activities. Start learning something new!</p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Link to="/dashboard/my-learning">
                <button className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-purple-600 bg-purple-100 hover:bg-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:hover:bg-purple-800">
                  <BookOpen className="h-3 w-3 mr-1" />
                  Browse Learning Content
                </button>
              </Link>
              <Link to="/dashboard/quizzes">
                <button className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900 dark:text-indigo-200 dark:hover:bg-indigo-800">
                  <Trophy className="h-3 w-3 mr-1" />
                  Browse Quizzes
                </button>
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* Footer with overall stats */}
      {sortedActivities.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Keep up the great work!</span>
            <Link to="/dashboard/progress" className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
              View Progress →
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}