import React from 'react';
import { Button } from '~/components/ui/button';
import { cn } from '~/lib/utils';

interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  completedSteps?: number[];
  onStepChange?: (step: number) => void;
  className?: string;
}

export function StepNavigation({
  currentStep,
  totalSteps,
  completedSteps = [],
  onStepChange,
  className
}: StepNavigationProps) {
  const handleStepClick = (stepIndex: number) => {
    if (onStepChange) {
      onStepChange(stepIndex);
    }
  };

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps.includes(stepIndex);
  };

  const isStepAccessible = (stepIndex: number) => {
    // Allow access to current step, completed steps, and next step
    return stepIndex <= currentStep || isStepCompleted(stepIndex);
  };

  return (
    <div className={cn("flex items-center justify-center space-x-4 py-6", className)}>
      {/* Progress Dots */}
      <div className="flex items-center space-x-2">
        {Array.from({ length: totalSteps }, (_, index) => {
          const isActive = index === currentStep;
          const isCompleted = isStepCompleted(index);
          const isAccessible = isStepAccessible(index);

          return (
            <button
              key={index}
              onClick={() => isAccessible && handleStepClick(index)}
              disabled={!isAccessible}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
                {
                  // Active step - purple and larger
                  "bg-purple-500 scale-125": isActive,
                  // Completed step - purple but smaller
                  "bg-purple-400 hover:bg-purple-500": isCompleted && !isActive,
                  // Future accessible step - gray with hover
                  "bg-gray-600 hover:bg-gray-500": !isCompleted && !isActive && isAccessible,
                  // Inaccessible step - very light gray
                  "bg-gray-800 cursor-not-allowed": !isAccessible,
                  // Add cursor pointer for accessible steps
                  "cursor-pointer": isAccessible,
                }
              )}
              title={`Step ${index + 1}${isCompleted ? ' (Completed)' : isActive ? ' (Current)' : ''}`}
            />
          );
        })}
      </div>

      {/* Step Counter */}
      <div className="text-sm text-muted-foreground ml-4">
        <span className="font-medium">{currentStep + 1}</span>
        <span className="mx-1">of</span>
        <span>{totalSteps}</span>
      </div>
    </div>
  );
}

interface StepNavigationWithControlsProps extends StepNavigationProps {
  onPrevious?: () => void;
  onNext?: () => void;
  showControls?: boolean;
  previousDisabled?: boolean;
  nextDisabled?: boolean;
}

export function StepNavigationWithControls({
  currentStep,
  totalSteps,
  completedSteps = [],
  onStepChange,
  onPrevious,
  onNext,
  showControls = true,
  previousDisabled = false,
  nextDisabled = false,
  className
}: StepNavigationWithControlsProps) {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className={cn("flex items-center justify-between py-6", className)}>
      {/* Previous Button */}
      {showControls && (
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep || previousDisabled}
          className="flex items-center space-x-2"
        >
          <span>Previous</span>
        </Button>
      )}

      {/* Step Navigation */}
      <StepNavigation
        currentStep={currentStep}
        totalSteps={totalSteps}
        completedSteps={completedSteps}
        onStepChange={onStepChange}
      />

      {/* Next Button */}
      {showControls && (
        <Button
          variant="outline"
          onClick={onNext}
          disabled={isLastStep || nextDisabled}
          className="flex items-center space-x-2"
        >
          <span>Next</span>
        </Button>
      )}
    </div>
  );
}
