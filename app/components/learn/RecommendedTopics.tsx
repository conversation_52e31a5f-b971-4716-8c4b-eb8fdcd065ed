'use client';

import { Brain, Code, Database, Globe, Smartphone, TrendingUp, Zap, BookOpen } from 'lucide-react';
import { Link } from 'react-router';
import { cn } from '~/lib/utils';

interface RecommendedTopicsProps {
  className?: string;
}

interface TopicTemplate {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  topic: string;
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  preferredContentTypes: string[];
  focusAreas: string;
  color: string;
}

const topicTemplates: TopicTemplate[] = [
  {
    id: 'javascript-basics',
    title: 'JavaScript Fundamentals',
    description: 'Learn the core concepts of JavaScript programming',
    icon: Code,
    topic: 'JavaScript fundamentals and basic programming concepts',
    learningLevel: 'beginner',
    preferredContentTypes: ['paragraph', 'bulletList', 'numberedList'],
    focusAreas: 'Variables, functions, loops, conditionals, and basic DOM manipulation',
    color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
  },
  {
    id: 'react-intro',
    title: 'React for Beginners',
    description: 'Get started with React and component-based development',
    icon: Brain,
    topic: 'React fundamentals and component development',
    learningLevel: 'beginner',
    preferredContentTypes: ['paragraph', 'numberedList', 'infoBox'],
    focusAreas: 'Components, JSX, props, state, and event handling',
    color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
  },
  {
    id: 'python-data-science',
    title: 'Python for Data Science',
    description: 'Explore data analysis and visualization with Python',
    icon: TrendingUp,
    topic: 'Python programming for data science and analysis',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'bulletList', 'table', 'comparison'],
    focusAreas: 'Pandas, NumPy, Matplotlib, data cleaning, and basic statistics',
    color: 'bg-green-50 border-green-200 hover:bg-green-100'
  },
  {
    id: 'sql-databases',
    title: 'SQL & Database Design',
    description: 'Master database queries and relational design',
    icon: Database,
    topic: 'SQL fundamentals and database design principles',
    learningLevel: 'beginner',
    preferredContentTypes: ['paragraph', 'numberedList', 'table', 'keyValueGrid'],
    focusAreas: 'SELECT queries, JOINs, database normalization, and basic optimization',
    color: 'bg-purple-50 border-purple-200 hover:bg-purple-100'
  },
  {
    id: 'web-apis',
    title: 'REST APIs & HTTP',
    description: 'Understand web APIs and HTTP protocols',
    icon: Globe,
    topic: 'REST API design and HTTP protocol fundamentals',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'bulletList', 'infoBox', 'comparison'],
    focusAreas: 'HTTP methods, status codes, API design patterns, and authentication',
    color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100'
  },
  {
    id: 'mobile-development',
    title: 'Mobile App Development',
    description: 'Build mobile apps with React Native or Flutter',
    icon: Smartphone,
    topic: 'Mobile application development fundamentals',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'numberedList', 'grid', 'comparison'],
    focusAreas: 'Cross-platform development, UI components, navigation, and device APIs',
    color: 'bg-pink-50 border-pink-200 hover:bg-pink-100'
  },
  {
    id: 'machine-learning',
    title: 'Machine Learning Basics',
    description: 'Introduction to ML algorithms and concepts',
    icon: Zap,
    topic: 'Machine learning fundamentals and basic algorithms',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'bulletList', 'comparison', 'infoBox'],
    focusAreas: 'Supervised learning, unsupervised learning, model evaluation, and common algorithms',
    color: 'bg-orange-50 border-orange-200 hover:bg-orange-100'
  },
  {
    id: 'algorithms-data-structures',
    title: 'Algorithms & Data Structures',
    description: 'Essential computer science fundamentals',
    icon: BookOpen,
    topic: 'Algorithms and data structures fundamentals',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'numberedList', 'comparison', 'table'],
    focusAreas: 'Arrays, linked lists, trees, sorting algorithms, and time complexity',
    color: 'bg-red-50 border-red-200 hover:bg-red-100'
  }
];

export function RecommendedTopics({ className }: RecommendedTopicsProps) {
  const createLearningUrl = (template: TopicTemplate) => {
    const params = new URLSearchParams({
      topic: template.topic,
      learningLevel: template.learningLevel,
      preferredContentTypes: template.preferredContentTypes.join(','),
      focusAreas: template.focusAreas
    });
    return `/dashboard/learn?${params.toString()}`;
  };

  return (
    <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Brain className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recommended Topics</h3>
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Quick start templates
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
        {topicTemplates.map((template) => {
          const IconComponent = template.icon;
          return (
            <Link
              key={template.id}
              to={createLearningUrl(template)}
              className={cn(
                'block p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500',
                template.color
              )}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <IconComponent className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                    {template.title}
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                    {template.description}
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                      template.learningLevel === 'beginner' && 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                      template.learningLevel === 'intermediate' && 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                      template.learningLevel === 'advanced' && 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    )}>
                      {template.learningLevel}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      Click to start →
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      <div className="mt-6 text-center">
        <Link 
          to="/dashboard/learn"
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <Brain className="h-4 w-4 mr-2" />
          Create Custom Learning Content
        </Link>
      </div>
    </div>
  );
}