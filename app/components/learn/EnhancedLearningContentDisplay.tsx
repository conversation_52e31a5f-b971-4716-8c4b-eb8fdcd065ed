import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { BookOpen, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '~/lib/utils';
import { LearningContentHeader } from './LearningContentHeader';
import { StepNavigation, StepNavigationWithControls } from './StepNavigation';
import { AskQuestionsSidebar } from './AskQuestionsSidebar';
import { MultiStepExplain } from '../templates/MultiStepExplain';
import type { StepConfig } from '../templates/types';

interface EnhancedLearningContentDisplayProps {
  contentId: string;
  title: string;
  description: string;
  steps: StepConfig[];
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  isPublic: boolean;
  initialStep?: number;
  completedSteps?: number[];
  progress?: number;
  onStepChange?: (step: number) => void;
  onProgressUpdate?: (progress: number) => void;
  className?: string;
}

export function EnhancedLearningContentDisplay({
  contentId,
  title,
  description,
  steps,
  learningLevel,
  estimatedReadingTime,
  isPublic,
  initialStep = 0,
  completedSteps = [],
  progress = 0,
  onStepChange,
  onProgressUpdate,
  className
}: EnhancedLearningContentDisplayProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);

  useEffect(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
    onStepChange?.(step);
    
    // Calculate and update progress
    const newProgress = ((step + 1) / steps.length) * 100;
    onProgressUpdate?.(newProgress);
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      handleStepChange(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      handleStepChange(currentStep + 1);
    }
  };

  const handleGenerateQuiz = () => {
    // TODO: Implement quiz generation
    console.log('Generate quiz for content:', contentId);
  };

  const handleNotesBookmarks = () => {
    // TODO: Implement notes and bookmarks
    console.log('Open notes and bookmarks for content:', contentId);
  };

  const handleRateFeedback = () => {
    // TODO: Implement rating and feedback
    console.log('Open rating and feedback for content:', contentId);
  };

  const handleShare = () => {
    // TODO: Implement sharing
    console.log('Share content:', contentId);
  };

  const handleGenerateSimilar = () => {
    // TODO: Implement generate similar content
    console.log('Generate similar content to:', contentId);
  };

  const handleCreateNew = () => {
    // TODO: Navigate to create new content
    console.log('Create new content');
  };

  const handleSendMessage = async (message: string): Promise<string> => {
    // TODO: Implement AI chat functionality
    // This would typically call an API to get AI responses
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(`I understand you're asking about "${message}". Let me help you with that aspect of ${title}. Could you be more specific about what you'd like to know?`);
      }, 1000);
    });
  };

  const currentStepData = steps[currentStep];

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      <div className="w-full">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Header */}
            <LearningContentHeader
              title={title}
              description={description}
              progress={progress}
              currentStep={currentStep}
              totalSteps={steps.length}
              estimatedReadingTime={estimatedReadingTime}
              learningLevel={learningLevel}
              isPublic={isPublic}
              onGenerateQuiz={handleGenerateQuiz}
              onNotesBookmarks={handleNotesBookmarks}
              onRateFeedback={handleRateFeedback}
              onShare={handleShare}
              onGenerateSimilar={handleGenerateSimilar}
              onCreateNew={handleCreateNew}
            />

            {/* Main Content */}
            <Card className="min-h-[500px]">
              <CardContent className="p-8">
                {/* Step Navigation */}
                <StepNavigation
                  currentStep={currentStep}
                  totalSteps={steps.length}
                  completedSteps={completedSteps}
                  onStepChange={handleStepChange}
                />

                {/* Step Title */}
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-purple-500/10 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-4 w-4 text-purple-500" />
                  </div>
                  <h2 className="text-xl font-semibold text-foreground">
                    {currentStepData?.title || `Step ${currentStep + 1}`}
                  </h2>
                </div>

                {/* Step Content */}
                <div className="prose prose-gray dark:prose-invert max-w-none">
                  <MultiStepExplain
                    steps={[currentStepData]}
                    initialStep={0}
                    completedSteps={completedSteps.includes(currentStep) ? [0] : []}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Navigation Controls */}
            <StepNavigationWithControls
              currentStep={currentStep}
              totalSteps={steps.length}
              completedSteps={completedSteps}
              onStepChange={handleStepChange}
              onPrevious={handlePrevious}
              onNext={handleNext}
              showControls={true}
            />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <AskQuestionsSidebar
                contentId={contentId}
                contentTitle={title}
                onSendMessage={handleSendMessage}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
