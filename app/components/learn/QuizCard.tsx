'use client';

import React from 'react';
import { <PERSON>, Trophy, Target, Clock, BookOpen, ArrowRight } from 'lucide-react';
import { Link } from 'react-router';
import { useGetAllQuizzes, Quiz } from '~/lib/hooks/use-quiz-api';

interface QuizCardProps {
  className?: string;
}

export function QuizCard({ className = '' }: QuizCardProps) {
  const { data: quizzesData, isLoading, error } = useGetAllQuizzes({
    limit: 50,
    offset: 0,
    includePublic: true,
  });

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="animate-pulse flex-1">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
            <div className="ml-4 flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-32"></div>
            </div>
          </div>
          <div className="mt-4 space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-full"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center flex-1">
          <div className="flex-shrink-0">
            <Brain className="h-8 w-8 text-gray-400" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">Quizzes</h3>
            <p className="text-sm text-red-500">Unable to load quiz data</p>
          </div>
        </div>
      </div>
    );
  }

  const quizzes: Quiz[] = quizzesData || [];

  // No data state (no quizzes available)
  if (quizzes.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <Brain className="h-8 w-8 text-indigo-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Quizzes</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Test your knowledge</p>
          </div>
        </div>
        
        <div className="text-center py-4 flex-1 flex flex-col justify-center">
          <BookOpen className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            No quizzes available yet. Create learning content first to generate quizzes!
          </p>
          <Link to="/dashboard/learn">
            <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Create Content
              <ArrowRight className="ml-2 h-4 w-4" />
            </button>
          </Link>
        </div>
      </div>
    );
  }

  // Calculate quiz statistics
  const totalQuizzes = quizzes.length;
  const easyQuizzes = quizzes.filter((q: Quiz) => q.difficulty === 'beginner').length;
  const mediumQuizzes = quizzes.filter((q: Quiz) => q.difficulty === 'intermediate').length;
  const hardQuizzes = quizzes.filter((q: Quiz) => q.difficulty === 'advanced').length;
  const totalQuestions = quizzes.reduce((sum: number, quiz: Quiz) => sum + (quiz.questions?.length || 0), 0);
  const averageDuration = totalQuizzes > 0 
    ? Math.round(quizzes.reduce((sum: number, quiz: Quiz) => sum + (quiz.metadata?.estimatedDuration || 0), 0) / totalQuizzes)
    : 0;

  // Format duration
  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex flex-col ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Brain className="h-8 w-8 text-indigo-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Quizzes</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Test your knowledge</p>
          </div>
        </div>
        <Link to="/dashboard/quizzes">
          <button className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
            View All →
          </button>
        </Link>
      </div>

      {/* Quiz Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Brain className="h-4 w-4 text-indigo-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{totalQuizzes}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Total Quizzes</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Target className="h-4 w-4 text-blue-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{totalQuestions}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Questions</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Clock className="h-4 w-4 text-purple-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{formatDuration(averageDuration)}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Avg Duration</p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Trophy className="h-4 w-4 text-yellow-600 mr-1" />
            <span className="text-2xl font-bold text-gray-900 dark:text-white">{hardQuizzes}</span>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">Hard Level</p>
        </div>
      </div>

      {/* Difficulty Distribution */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Difficulty Distribution</span>
        </div>
        <div className="flex space-x-1">
          {easyQuizzes > 0 && (
            <div 
              className="bg-green-500 h-2 rounded-l-full" 
              style={{ width: `${(easyQuizzes / totalQuizzes) * 100}%` }}
              title={`Easy: ${easyQuizzes} quizzes`}
            ></div>
          )}
          {mediumQuizzes > 0 && (
            <div 
              className="bg-yellow-500 h-2" 
              style={{ width: `${(mediumQuizzes / totalQuizzes) * 100}%` }}
              title={`Medium: ${mediumQuizzes} quizzes`}
            ></div>
          )}
          {hardQuizzes > 0 && (
            <div 
              className="bg-red-500 h-2 rounded-r-full" 
              style={{ width: `${(hardQuizzes / totalQuizzes) * 100}%` }}
              title={`Hard: ${hardQuizzes} quizzes`}
            ></div>
          )}
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span className="flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
            Easy ({easyQuizzes})
          </span>
          <span className="flex items-center">
            <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
            Medium ({mediumQuizzes})
          </span>
          <span className="flex items-center">
            <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
            Hard ({hardQuizzes})
          </span>
        </div>
      </div>

      {/* Recent Activity */}
      {totalQuizzes > 0 && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
            {totalQuizzes} quiz{totalQuizzes !== 1 ? 'es' : ''} available
          </span>
        </div>
      )}
    </div>
  );
}