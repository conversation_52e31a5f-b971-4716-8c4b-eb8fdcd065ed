import React from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { 
  FileQuestion, 
  BookmarkPlus, 
  Star, 
  Share2, 
  <PERSON>rk<PERSON>, 
  Plus,
  Clock,
  User,
  Brain
} from 'lucide-react';
import { cn } from '~/lib/utils';

interface LearningContentHeaderProps {
  title: string;
  description: string;
  progress: number; // 0-100
  currentStep: number;
  totalSteps: number;
  estimatedReadingTime: number;
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  isPublic: boolean;
  onGenerateQuiz?: () => void;
  onNotesBookmarks?: () => void;
  onRateFeedback?: () => void;
  onShare?: () => void;
  onGenerateSimilar?: () => void;
  onCreateNew?: () => void;
  className?: string;
}

export function LearningContentHeader({
  title,
  description,
  progress,
  currentStep,
  totalSteps,
  estimatedReadingTime,
  learningLevel,
  isPublic,
  onGenerateQuiz,
  onNotesBookmarks,
  onRateFeedback,
  onShare,
  onGenerateSimilar,
  onCreateNew,
  className
}: LearningContentHeaderProps) {
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'intermediate':
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'advanced':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      default:
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  return (
    <Card className={cn("border-0 bg-background", className)}>
      <CardHeader className="pb-4">
        {/* AI Generated Badge */}
        <div className="flex items-center justify-between mb-4">
          <Badge variant="outline" className="bg-purple-500/10 text-purple-400 border-purple-500/20">
            <Brain className="h-3 w-3 mr-1" />
            AI Generated
          </Badge>
        </div>

        {/* Title and Description */}
        <div className="space-y-3">
          <h1 className="text-2xl font-bold text-foreground">{title}</h1>
          <p className="text-muted-foreground leading-relaxed">{description}</p>
        </div>

        {/* Progress and Metadata */}
        <div className="flex items-center justify-between mt-6">
          <div className="flex items-center space-x-6 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {estimatedReadingTime} min read
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              {learningLevel.charAt(0).toUpperCase() + learningLevel.slice(1)}
            </div>
            <div className="flex items-center">
              <FileQuestion className="h-4 w-4 mr-1" />
              {totalSteps} steps
            </div>
            <div className="flex items-center">
              <Share2 className="h-4 w-4 mr-1" />
              {isPublic ? 'Public' : 'Private'}
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Badge className={getLevelColor(learningLevel)}>
              {learningLevel}
            </Badge>
            <div className="text-sm text-muted-foreground">
              <span className="text-lg font-semibold text-foreground">{Math.round(progress)}%</span>
              <span className="ml-1">Step {currentStep + 1}</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Action Buttons */}
        <div className="flex items-center space-x-2 flex-wrap gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onGenerateQuiz}
            className="flex items-center space-x-2"
          >
            <FileQuestion className="h-4 w-4" />
            <span>Generate Quiz</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onNotesBookmarks}
            className="flex items-center space-x-2"
          >
            <BookmarkPlus className="h-4 w-4" />
            <span>Notes & Bookmarks</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onRateFeedback}
            className="flex items-center space-x-2"
          >
            <Star className="h-4 w-4" />
            <span>Rate & Feedback</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onShare}
            className="flex items-center space-x-2"
          >
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onGenerateSimilar}
            className="flex items-center space-x-2"
          >
            <Sparkles className="h-4 w-4" />
            <span>Generate Similar</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onCreateNew}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create New</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
