import { ReactNode } from 'react';

export interface StepConfig {
  title: string;
  icon: ReactNode | string; // Can be ReactNode for direct use or string for builder
  type: 'paragraph'
    | 'infoBox'
    | 'bulletList'
    | 'numberedList'
    | 'grid'
    | 'comparison'
    | 'table'
    | 'scatterPlot'
    | 'keyValueGrid';
  data: any; // structured data matching the chosen type
}

export interface MultiStepExplainProps {
  steps: StepConfig[];
  className?: string;
  initialStep?: number;
  onStepChange?: (step: number, isLessonComplete?: boolean) => void;
  completedSteps?: number[];
}

// Specific data type interfaces for better type safety
export interface InfoBoxData {
  heading?: string;
  lines: string[];
}

export interface GridData {
  title: string;
  content: string;
}

export interface ComparisonData {
  label: string;
  before: string;
  after: string;
}

export interface TableData {
  headers: string[];
  rows: string[][];
}

export interface ScatterPlotConfig {
  title: string;
  xLabel: string;
  yLabel: string;
  points: Array<{
    x: number;
    y: number;
    label?: string;
    color?: string;
  }>;
}

export interface KeyValueData {
  key: string;
  value: string;
  description?: string;
}
