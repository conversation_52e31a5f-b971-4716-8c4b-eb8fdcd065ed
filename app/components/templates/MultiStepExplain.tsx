'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, ChevronLeft, RotateCcw } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { cn } from '~/lib/utils';
import confetti from 'canvas-confetti';
import {
  MultiStepExplainProps,
  StepConfig,
  InfoBoxData,
  GridData,
  ComparisonData,
  TableData,
  ScatterPlotConfig,
  KeyValueData
} from './types';

export function MultiStepExplain({
  steps,
  className = '',
  initialStep = 0,
  onStepChange,
  completedSteps: externalCompletedSteps = [],
}: MultiStepExplainProps) {
  // Trophy animations are now handled in LearningContentDisplay component
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const previousCompletedStepsRef = useRef<Set<number>>(new Set());

  // Sync internal state with initialStep prop changes
  useEffect(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);

  // Sync external completed steps with internal state
  useEffect(() => {
    setCompletedSteps(new Set(externalCompletedSteps));
  }, [externalCompletedSteps]);

  // Trigger confetti animation
  const triggerConfetti = () => {
    const count = 200;
    const defaults = {
      origin: { y: 0.7 }
    };

    function fire(particleRatio: number, opts: any) {
      confetti({
        ...defaults,
        ...opts,
        particleCount: Math.floor(count * particleRatio)
      });
    }

    fire(0.25, {
      spread: 26,
      startVelocity: 55,
    });

    fire(0.2, {
      spread: 60,
    });

    fire(0.35, {
      spread: 100,
      decay: 0.91,
      scalar: 0.8
    });

    fire(0.1, {
      spread: 120,
      startVelocity: 25,
      decay: 0.92,
      scalar: 1.2
    });

    fire(0.1, {
      spread: 120,
      startVelocity: 45,
    });
  };

  const nextStep = () => {
    // Mark current step as completed (trophy animation handled in parent after successful save)
    if (!completedSteps.has(currentStep)) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
    }

    if (currentStep < steps.length - 1) {
      // Move to next step
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      if (onStepChange) {
        onStepChange(newStep);
      }
    } else {
      // On final step - trigger confetti and ensure progress is saved
      triggerConfetti();
      if (onStepChange) {
        // Call onStepChange with lesson completion flag to ensure final step is marked as completed
        onStepChange(currentStep, true);
      }
    }
  };

  const restartLesson = () => {
    // Navigate back to first step
    setCurrentStep(0);
    if (onStepChange) {
      onStepChange(0);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      if (onStepChange) {
        onStepChange(newStep);
      }
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex);
      if (onStepChange) {
        onStepChange(stepIndex);
      }
    }
  };

  // Content renderers
  const renderParagraph = (data: string | string[]) => {
    const paragraphs = Array.isArray(data) ? data : [data];
    return (
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => (
          <p key={index} className="text-gray-700 dark:text-gray-300 leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    );
  };

  const renderInfoBox = (data: InfoBoxData) => {
    return (
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        {data.heading && (
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
            {data.heading}
          </h3>
        )}
        <div className="space-y-2">
          {data.lines.map((line, index) => (
            <p key={index} className="text-blue-700 dark:text-blue-300">
              {line}
            </p>
          ))}
        </div>
      </div>
    );
  };

  const renderBulletList = (data: string[]) => {
    return (
      <ul className="space-y-2 list-disc list-inside">
        {data.map((item, index) => (
          <li key={index} className="text-gray-700 dark:text-gray-300">
            {item}
          </li>
        ))}
      </ul>
    );
  };

  const renderNumberedList = (data: string[]) => {
    return (
      <ol className="space-y-2 list-decimal list-inside">
        {data.map((item, index) => (
          <li key={index} className="text-gray-700 dark:text-gray-300">
            {item}
          </li>
        ))}
      </ol>
    );
  };

  const renderGrid = (data: GridData[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">
              {item.title}
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              {item.content}
            </p>
          </div>
        ))}
      </div>
    );
  };

  const renderComparison = (data: ComparisonData[]) => {
    return (
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                {item.label}
              </h4>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2">
              <div className="p-4 bg-red-50 dark:bg-red-900/20">
                <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">Before</h5>
                <p className="text-red-700 dark:text-red-300">{item.before}</p>
              </div>
              <div className="p-4 bg-green-50 dark:bg-green-900/20">
                <h5 className="font-medium text-green-800 dark:text-green-200 mb-2">After</h5>
                <p className="text-green-700 dark:text-green-300">{item.after}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTable = (data: TableData) => {
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200 dark:border-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              {data.headers.map((header, index) => (
                <th key={index} className="px-4 py-2 text-left font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-b border-gray-200 dark:border-gray-700">
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="px-4 py-2 text-gray-700 dark:text-gray-300">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderKeyValueGrid = (data: KeyValueData[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <span className="font-semibold text-gray-800 dark:text-gray-200">
                {item.key}
              </span>
              <span className="text-blue-600 dark:text-blue-400 font-mono">
                {item.value}
              </span>
            </div>
            {item.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {item.description}
              </p>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderScatterPlot = (data: ScatterPlotConfig) => {
    // Simple scatter plot implementation
    const maxX = Math.max(...data.points.map(p => p.x));
    const maxY = Math.max(...data.points.map(p => p.y));
    
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 text-center">
          {data.title}
        </h4>
        <div className="relative w-full h-64 bg-gray-50 dark:bg-gray-900 rounded border">
          {/* Y-axis label */}
          <div className="absolute -left-8 top-1/2 transform -rotate-90 text-sm text-gray-600 dark:text-gray-400">
            {data.yLabel}
          </div>
          
          {/* Plot area */}
          <div className="relative w-full h-full p-4">
            {data.points.map((point, index) => (
              <div
                key={index}
                className="absolute w-2 h-2 rounded-full"
                style={{
                  left: `${(point.x / maxX) * 90}%`,
                  bottom: `${(point.y / maxY) * 90}%`,
                  backgroundColor: point.color || '#3B82F6',
                }}
                title={point.label || `(${point.x}, ${point.y})`}
              />
            ))}
          </div>
          
          {/* X-axis label */}
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-sm text-gray-600 dark:text-gray-400">
            {data.xLabel}
          </div>
        </div>
      </div>
    );
  };

  const renderContent = (step: StepConfig) => {
    switch (step.type) {
      case 'paragraph':
        return renderParagraph(step.data);
      case 'infoBox':
        return renderInfoBox(step.data);
      case 'bulletList':
        return renderBulletList(step.data);
      case 'numberedList':
        return renderNumberedList(step.data);
      case 'grid':
        return renderGrid(step.data);
      case 'comparison':
        return renderComparison(step.data);
      case 'table':
        return renderTable(step.data);
      case 'scatterPlot':
        return renderScatterPlot(step.data);
      case 'keyValueGrid':
        return renderKeyValueGrid(step.data);
      default:
        return <div className="text-gray-500">Unknown content type: {step.type}</div>;
    }
  };

  if (!steps || steps.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 p-8">
        No steps available
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-gray-50 dark:bg-gray-900", className)}>
      {/* Header with navigation and progress */}
      <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Navigation buttons */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={currentStep === steps.length - 1 ? restartLesson : nextStep}
              className="flex items-center"
            >
              {currentStep === steps.length - 1 ? (
                <>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Restart
                </>
              ) : (
                <>
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </>
              )}
            </Button>
          </div>

          {/* Step counter */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Step {currentStep + 1} of {steps.length}
          </div>

          {/* Step dots */}
          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <div key={index} className="relative">
                <div
                  className={cn(
                    "w-3 h-3 rounded-full cursor-pointer transition-colors",
                    index === currentStep ? 'bg-blue-500 dark:bg-blue-400' :
                    completedSteps.has(index) ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                  )}
                  onClick={() => goToStep(index)}
                  title={`Go to step ${index + 1}`}
                />
                {/* Trophy animations are now handled in LearningContentDisplay component */}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto scrollbar-thin px-6">
        {/* Current step */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-8">
          <div className="flex items-center justify-center mb-6">
            {steps[currentStep].icon}
            <h2 className="text-2xl font-bold ml-3 text-gray-800 dark:text-gray-200">
              {steps[currentStep].title}
            </h2>
          </div>

          <div className="mb-8">
            {renderContent(steps[currentStep])}
          </div>
        </div>
      </div>
    </div>
  );
}
